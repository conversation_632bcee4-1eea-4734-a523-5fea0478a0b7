package types

// ProgramStatus holds the name and enabled status of a program
type ProgramStatus struct {
	Name    string
	Enabled bool
}

// Setpoint represents a single setpoint configuration
type Setpoint struct {
	Name  string
	Value float64
}

// Period represents a time period with enhanced features
type Period struct {
	PeriodId     string             `json:"periodId"`
	Name         string             `json:"name"`
	Enabled      bool               `json:"enabled"`
	PeriodStatus string             `json:"periodStatus"` // active/inactive/disabled/externalNotActive/notUsedOverlap
	StartTime    string             `json:"startTime"`    // Supports "HH:MM" or "HH:MM relativeDawnDusk"
	EndTime      string             `json:"endTime"`      // Supports "HH:MM" or "HH:MM relativeDawnDusk"
	ActiveDays   map[string]bool    `json:"activeDays"`
	Setpoints    map[string]float64 `json:"setpoints"`
}

// DiurnalSchedule represents a single schedule with enhanced features
type DiurnalSchedule struct {
	ScheduleId  string   `json:"scheduleId"`
	ProgramName string   `json:"programName"`
	Enabled     bool     `json:"enabled"`
	HubId       string   `json:"hubId"`  // Hub ID for Redis key format
	ZoneId      string   `json:"zoneId"` // Zone ID for Redis key format
	Periods     []Period `json:"periods"`
}

// DiurnalSetpointConfig represents the enhanced configuration structure
type DiurnalSetpointConfig struct {
	Schedules []DiurnalSchedule `json:"schedules"`
}

// TimeReference represents the relative time reference types
type TimeReference string

const (
	BeforeDawn TimeReference = "beforeDawn"
	AfterDawn  TimeReference = "afterDawn"
	BeforeDusk TimeReference = "beforeDusk"
	AfterDusk  TimeReference = "afterDusk"
)

// ParsedTime represents a parsed time with optional relative reference
type ParsedTime struct {
	Hour       int           `json:"hour"`
	Minute     int           `json:"minute"`
	Reference  TimeReference `json:"reference,omitempty"`
	IsRelative bool          `json:"isRelative"`
}

// PeriodState represents the current state of a diurnal schedule
type PeriodState int

const (
	StateIdle     PeriodState = iota // Outside all periods and ramps
	StateInPeriod                    // Currently within an active period
	StateRamping                     // Currently ramping between periods
)

// String returns a string representation of the PeriodState
func (ps PeriodState) String() string {
	switch ps {
	case StateIdle:
		return "Idle"
	case StateInPeriod:
		return "In Period"
	case StateRamping:
		return "Ramping"
	default:
		return "Unknown"
	}
}

// DiurnalState represents the current state of a diurnal schedule
type DiurnalState struct {
	ScheduleID     string             `json:"scheduleId"`
	State          PeriodState        `json:"state"`
	CurrentPeriod  *Period            `json:"currentPeriod,omitempty"`
	NextPeriod     *Period            `json:"nextPeriod,omitempty"`
	CurrentValues  map[string]float64 `json:"currentValues"`
	MinutesElapsed float64            `json:"minutesElapsed,omitempty"`
	LastUpdated    string             `json:"lastUpdated"`
}

// SetpointPurpose defines the functional role of a setpoint in the CEB system
type SetpointPurpose string

const (
	PurposeHeatingTarget        SetpointPurpose = "heatingTarget"
	PurposeCoolingTarget        SetpointPurpose = "coolingTarget"
	PurposeDehumidifyHeatTarget SetpointPurpose = "dehumidifyHeatTarget"
	PurposeDehumidifyVentTarget SetpointPurpose = "dehumidifyVentTarget"
	PurposeMaxDehumidVent       SetpointPurpose = "maxDehumidVent"
	PurposeMaxDehumidHeat       SetpointPurpose = "maxDehumidHeat"
	PurposeCO2Target            SetpointPurpose = "co2Target"
	PurposeSpareTarget          SetpointPurpose = "spareTarget"
)

// CEBInputRedisKeys represents the input Redis key mappings for CEB system
type CEBInputRedisKeys struct {
	ZoneTemperature                  string `json:"zoneTemperature"`
	BackupZoneTemperature            string `json:"backupZoneTemperature"`
	ZoneHumidity                     string `json:"zoneHumidity"`
	ShadePosition                    string `json:"shadePosition"`
	CoolingTarget                    string `json:"coolingTarget"`
	HeatingTarget                    string `json:"heatingTarget"`
	DehumidifyVentilationTarget      string `json:"dehumidifyVentilationTarget"`
	DehumidifyHeatingTarget          string `json:"dehumidifyHeatingTarget"`
	MaxLimitForDehumidifyVentilation string `json:"maxLimitForDehumidifyVentilation"`
	MaxLimitForDehumidifyHeating     string `json:"maxLimitForDehumidifyHeating"`
	OutdoorTemperature               string `json:"outdoorTemperature"`
	LightLevelNI                     string `json:"lightLevelNI"`
	LightLevelI                      string `json:"lightLevelI"`
}

// CEBOutputRedisKeys represents the output Redis key mappings for CEB system
type CEBOutputRedisKeys struct {
	VentTempControl          string `json:"ventTempControl"`
	VentHumidityControl      string `json:"ventHumidityControl"`
	HighestVentRequest       string `json:"highestVentRequest"`
	SumVentRequests          string `json:"sumVentRequests"`
	HeatTempControl          string `json:"heatTempControl"`
	HeatHumidityControl      string `json:"heatHumidityControl"`
	HighestHeatRequest       string `json:"highestHeatRequest"`
	SumHeatRequests          string `json:"sumHeatRequests"`
	HeatingSystemTempRequest string `json:"heatingSystemTempRequest"`
	IntegratedTemp           string `json:"integratedTemp"`
	IntegratedHumidity       string `json:"integratedHumidity"`
}

// CrossModuleInput represents an input with cross-module request configuration
type CrossModuleInput struct {
	Used               bool   `json:"used"`
	Address            string `json:"address"`
	CrossModuleReqTime int    `json:"crossModuleReqTime"`
}

// ScalingSettings represents scaling configuration for effects
type ScalingSettings struct {
	MinInput  float64 `json:"minInput"`
	MaxInput  float64 `json:"maxInput"`
	MinOutput float64 `json:"minOutput"`
	MaxOutput float64 `json:"maxOutput"`
}

// ShadeSettings represents shade-specific temperature effect settings
type ShadeSettings struct {
	MinTempDifference float64 `json:"minTempDifference"`
	MaxTempDifference float64 `json:"maxTempDifference"`
	MinOutdoorEffect  float64 `json:"minOutdoorEffect"`
	MaxOutdoorEffect  float64 `json:"maxOutdoorEffect"`
}

// HeatReqForTemperatureControl represents heating temperature control configuration
type HeatReqForTemperatureControl struct {
	HeatingTarget            CrossModuleInput `json:"heatingTarget"`
	ZoneTemperature          CrossModuleInput `json:"zoneTemperature"`
	BackupZoneTemperature    CrossModuleInput `json:"backupZoneTemperature"`
	HeatingProportionalSpanP float64          `json:"heatingProportionalSpanP"`
	HeatingIntegralTimeI     int              `json:"heatingIntegralTimeI"`
}

// HeatReqForDehumidification represents heating dehumidification control configuration
type HeatReqForDehumidification struct {
	DehumidificationLimit           CrossModuleInput `json:"dehumidificationLimit"`
	CurrentHumidity                 CrossModuleInput `json:"currentHumidity"`
	DehumidifyHeatTarget            CrossModuleInput `json:"dehumidifyHeatTarget"`
	DehumidifyHeatOffset            float64          `json:"dehumidifyHeatOffset"`
	DehumidifyHeatProportionalSpanP float64          `json:"dehumidifyHeatProportionalSpanP"`
	DehumidifyHeatIntegralTimeI     int              `json:"dehumidifyHeatIntegralTimeI"`
}

// HeatingOutdoorTemperatureEffect represents heating outdoor temperature effect configuration
type HeatingOutdoorTemperatureEffect struct {
	HeatingTarget          CrossModuleInput `json:"heatingTarget"`
	OutdoorTemperature     CrossModuleInput `json:"outdoorTemperature"`
	ShadePosition          CrossModuleInput `json:"shadePosition"`
	ShadeRetractedSettings ShadeSettings    `json:"shadeRetractedSettings"`
	ShadeExtendedSettings  ShadeSettings    `json:"shadeExtendedSettings"`
}

// HeatingLightEffect represents heating light effect configuration
type HeatingLightEffect struct {
	CurrentLightReading     CrossModuleInput `json:"currentLightReading"`
	IntegratedLightReading  CrossModuleInput `json:"integratedLightReading"`
	LightPredictionModifier float64          `json:"lightPredictionModifier"`
	LightEffectScaling      ScalingSettings  `json:"lightEffectScaling"`
}

// HeatingSystemRequest represents heating system request configuration
type HeatingSystemRequest struct {
	RequestNumber        int     `json:"requestNumber"`
	UseHigherOrSum       string  `json:"useHigherOrSum"`
	MinHeatingSystemTemp float64 `json:"minHeatingSystemTemp"`
	MaxHeatingSystemTemp float64 `json:"maxHeatingSystemTemp"`
}

// HeatTuning represents the complete heat tuning configuration
type HeatTuning struct {
	HeatReqForTemperatureControl    HeatReqForTemperatureControl    `json:"heatReqForTemperatureControl"`
	HeatReqForDehumidification      HeatReqForDehumidification      `json:"heatReqForDehumidification"`
	HeatingOutdoorTemperatureEffect HeatingOutdoorTemperatureEffect `json:"heatingOutdoorTemperatureEffect"`
	HeatingLightEffect              HeatingLightEffect              `json:"heatingLightEffect"`
	HeatingSystemRequest            HeatingSystemRequest            `json:"heatingSystemRequest"`
}

// VentReqForTemperatureControl represents ventilation temperature control configuration
type VentReqForTemperatureControl struct {
	CoolingTarget            CrossModuleInput `json:"coolingTarget"`
	ZoneTemperature          CrossModuleInput `json:"zoneTemperature"`
	BackupZoneTemperature    CrossModuleInput `json:"backupZoneTemperature"`
	CoolingProportionalSpanP float64          `json:"coolingProportionalSpanP"`
	CoolingIntegralTimeI     int              `json:"coolingIntegralTimeI"`
}

// VentReqForDehumidification represents ventilation dehumidification control configuration
type VentReqForDehumidification struct {
	DehumidifyVentTarget                   CrossModuleInput `json:"dehumidifyVentTarget"`
	ZoneHumidity                           CrossModuleInput `json:"zoneHumidity"`
	MaxLimitForDehumidifyVentilation       CrossModuleInput `json:"maxLimitForDehumidifyVentilation"`
	VentilationDehumidifyProportionalSpanP float64          `json:"ventilationDehumidifyProportionalSpanP"`
	IntegralAccumulationTimeI              int              `json:"integralAccumulationTimeI"`
}

// VentilationOutdoorTemperatureEffect represents ventilation outdoor temperature effect configuration
type VentilationOutdoorTemperatureEffect struct {
	CoolingTarget            CrossModuleInput `json:"coolingTarget"`
	OutdoorTemperature       CrossModuleInput `json:"outdoorTemperature"`
	VentilationEffectScaling ScalingSettings  `json:"ventilationEffectScaling"`
}

// VentilationLightEffect represents ventilation light effect configuration
type VentilationLightEffect struct {
	CurrentLightReading     CrossModuleInput `json:"currentLightReading"`
	IntegratedLightReading  CrossModuleInput `json:"integratedLightReading"`
	LightPredictionModifier float64          `json:"lightPredictionModifier"`
	LightEffectScaling      ScalingSettings  `json:"lightEffectScaling"`
}

// VentilationSystemRequest represents ventilation system request configuration
type VentilationSystemRequest struct {
	RequestNumber      int     `json:"requestNumber"`
	UseHigherOrSum     string  `json:"useHigherOrSum"`
	MinVentilationRate float64 `json:"minVentilationRate"`
	MaxVentilationRate float64 `json:"maxVentilationRate"`
}

// VentilationTuning represents the complete ventilation tuning configuration
type VentilationTuning struct {
	VentReqForTemperatureControl        VentReqForTemperatureControl        `json:"ventReqForTemperatureControl"`
	VentReqForDehumidification          VentReqForDehumidification          `json:"ventReqForDehumidification"`
	VentilationOutdoorTemperatureEffect VentilationOutdoorTemperatureEffect `json:"ventilationOutdoorTemperatureEffect"`
	VentilationLightEffect              VentilationLightEffect              `json:"ventilationLightEffect"`
}

// CEBConfig represents the complete CEB system configuration
type CEBConfig struct {
	ProgramName string `json:"programName"`
	Enabled     bool   `json:"enabled"`

	// Enhanced configuration structure from plan
	CEBInputRedisKeys  *CEBInputRedisKeys  `json:"CEBInputRedisKeys,omitempty"`
	CEBOutputRedisKeys *CEBOutputRedisKeys `json:"CEBOutputRedisKeys,omitempty"`
	HeatTuning         *HeatTuning         `json:"heatTuning,omitempty"`
	VentilationTuning  *VentilationTuning  `json:"ventilationTuning,omitempty"`
}

// IsEnhancedConfig returns true if the configuration uses the enhanced structure
func (c *CEBConfig) IsEnhancedConfig() bool {
	return c.CEBInputRedisKeys != nil && c.HeatTuning != nil && c.VentilationTuning != nil
}

// GetOutputRedisKeys returns the appropriate output Redis keys based on configuration type
func (c *CEBConfig) GetOutputRedisKeys() *CEBOutputRedisKeys {
	// Use the top-level CEBOutputRedisKeys
	if c.CEBOutputRedisKeys != nil {
		return c.CEBOutputRedisKeys
	}

	// Return default keys if not configured
	return &CEBOutputRedisKeys{
		VentTempControl:          "hub:h1:zone:z1:instance:I1:ceb:ventTempControl",
		VentHumidityControl:      "hub:h1:zone:z1:instance:I1:ceb:ventHumidityControl",
		HighestVentRequest:       "hub:h1:zone:z1:instance:I1:ceb:highestVentRequest",
		SumVentRequests:          "hub:h1:zone:z1:instance:I1:ceb:sumVentRequests",
		HeatTempControl:          "hub:h1:zone:z1:instance:I1:ceb:heatTempControl",
		HeatHumidityControl:      "hub:h1:zone:z1:instance:I1:ceb:heatHumidityControl",
		HighestHeatRequest:       "hub:h1:zone:z1:instance:I1:ceb:highestHeatRequest",
		SumHeatRequests:          "hub:h1:zone:z1:instance:I1:ceb:sumHeatRequests",
		HeatingSystemTempRequest: "hub:h1:zone:z1:instance:I1:ceb:heatingSystemTempRequest",
		IntegratedTemp:           "hub:h1:zone:z1:instance:I1:ceb:integratedTemp",
		IntegratedHumidity:       "hub:h1:zone:z1:instance:I1:ceb:integratedHumidity",
	}
}

// CEBState represents the current state of CEB calculations
type CEBState struct {
	HeatReqForTemperatureControl float64 `json:"heatReqForTemperatureControl"` // 5. Heating Required for Temperature Control
	VentReqForTemperatureControl float64 `json:"ventReqForTemperatureControl"` // 1. Ventilation Required for Temperature Control
	HeatReqForHumidityControl    float64 `json:"heatReqForHumidityControl"`    // 6. Heating Required for Humidity Control
	VentReqForHumidityControl    float64 `json:"ventReqForHumidityControl"`    // 2. Ventilation Required for Humidity Control
	HighestHeatRequest           float64 `json:"highestHeatRequest"`           // 7. Highest Heating Request
	HighestVentRequest           float64 `json:"highestVentRequest"`           // 3. Highest Ventilation Request
	SumOfVentRequests            float64 `json:"sumOfVentRequests"`            // 4. Sum of Ventilation Requests
	SumOfHeatRequests            float64 `json:"sumOfHeatRequests"`            // 8. Sum of Heating Requests
	HeatingSystemTempRequest     float64 `json:"heatingSystemTempRequest"`     // 9. Current Temperature Request to Heating System
	IntegratedTemp               float64 `json:"integratedTemp"`
	IntegratedHumidity           float64 `json:"integratedHumidity"`
	OutdoorTemp                  float64 `json:"outdoorTemp"`
	LightLevel                   float64 `json:"lightLevel"`
}

// ClockRedisTopics represents Redis topic configuration for Clock Program
type ClockRedisTopics struct {
	CurrentTime string `json:"currentTime"`
	Date        string `json:"date"`
	DayOfWeek   string `json:"dayOfWeek"`
	Dawn        string `json:"dawn"`
	Dusk        string `json:"dusk"`
	IsDaytime   string `json:"isDaytime"`
}

// ClockConfig represents the configuration for the Clock Program
type ClockConfig struct {
	ProgramName    string           `json:"programName"`
	Enabled        bool             `json:"enabled"`
	Latitude       float64          `json:"latitude"`
	Longitude      float64          `json:"longitude"`
	Timezone       string           `json:"timezone"`
	NTPServer      string           `json:"ntpServer"`
	UpdateInterval int              `json:"updateInterval"` // seconds
	RedisTopics    ClockRedisTopics `json:"redisTopics"`
}

// ClockState represents the current state of the Clock Program
type ClockState struct {
	CurrentTime string `json:"currentTime"`
	Date        string `json:"date"`
	DayOfWeek   string `json:"dayOfWeek"`
	Dawn        string `json:"dawn"`
	Dusk        string `json:"dusk"`
	IsDaytime   bool   `json:"isDaytime"`
}
