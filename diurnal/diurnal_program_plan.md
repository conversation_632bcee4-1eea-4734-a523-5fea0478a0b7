# Diurnal Program Plan

## Input

### Input Format

```json
{
    "schedules": [
        {
            "scheduleId": 1,
            "programName": "Diurnal Setpoint",
            "periods": [
                {
                    "periodId": 1,
                    "name": "Morning Warmup",
                    "enabled": "true/false as boolean",
                    "periodStatus": "active/inactive/disabled/externalNotActive/notUsedOverlap",
                    "startTime": "01:00 beforeDawn",
                    "endTime": "02:00 afterDawn",
                    // 4 options: beforeDawn, afterDawn, beforeDusk, afterDusk
                    "activeDays": {
                        "monday": true,
                        "tuesday": true,
                        "wednesday": true,
                        "thursday": true,
                        "friday": true,
                        "saturday": true,
                        "sunday": false
                    },
                    "setpoints": {
                        // these 8 setpoint names can be anything
                        "heatingTarget": "custom value °C",
                        "coolingTarget": "custom value °C",
                        "dehumidifyHeatTarget": "custom value %Rh",
                        "dehumidifyVentTarget": "custom value %Rh",
                        "maxDehumidVent": "custom value %",
                        "maxDehumidHeat": "custom value %",
                        "co2Target": "custom value ppm",
                        "spareTarget": "custom value"
                    }
                }
            ]
        }
    ]
}
```

-   Example

```json
{
    "schedules": [
        {
            "scheduleId": 1,
            "programName": "Diurnal Setpoint",
            "enabled": true,
            "periods": [
                {
                    "periodId": 1,
                    "name": "Morning Warmup",
                    "enabled": true,
                    "periodStatus": "active",
                    "startTime": "01:00 beforeDawn",
                    "endTime": "02:00 afterDawn",
                    "activeDays": {
                        "monday": true,
                        "tuesday": true,
                        "wednesday": true,
                        "thursday": true,
                        "friday": true,
                        "saturday": true,
                        "sunday": false
                    },
                    "setpoints": {
                        "heatingTarget": 15.0,
                        "coolingTarget": 20.0,
                        "dehumidifyHeatTarget": 80.0,
                        "dehumidifyVentTarget": 40.0,
                        "maxDehumidVent": 15.0,
                        "maxDehumidHeat": 10.0,
                        "co2Target": 800,
                        "spareTarget": 0
                    }
                },
                {
                    "periodId": 2,
                    "name": "Day Period",
                    "enabled": true,
                    "periodStatus": "inactive",
                    "startTime": "02:00 afterDawn",
                    "endTime": "03:00 beforeDusk",
                    "activeDays": {
                        "monday": true,
                        "tuesday": true,
                        "wednesday": true,
                        "thursday": true,
                        "friday": true,
                        "saturday": true,
                        "sunday": false
                    },
                    "setpoints": {
                        "heatingTarget": 18.0,
                        "coolingTarget": 22.0,
                        "dehumidifyHeatTarget": 70.0,
                        "dehumidifyVentTarget": 50.0,
                        "maxDehumidVent": 20.0,
                        "maxDehumidHeat": 10.0,
                        "co2Target": 800,
                        "spareTarget": 0
                    }
                }
            ]
        },
        {
            "scheduleId": 2,
            "programName": "Diurnal Setpoint",
            "enabled": false,
            "periods": [
                {
                    "periodId": 1,
                    "name": "Morning Warmup",
                    "enabled": true,
                    "periodStatus": "disabled",
                    "startTime": "01:00 beforeDawn",
                    "endTime": "02:00 afterDawn",
                    "activeDays": {
                        "monday": true,
                        "tuesday": true,
                        "wednesday": true,
                        "thursday": true,
                        "friday": true,
                        "saturday": true,
                        "sunday": false
                    },
                    "setpoints": {
                        "heatingTarget": 15.0,
                        "coolingTarget": 20.0,
                        "dehumidifyHeatTarget": 80.0,
                        "dehumidifyVentTarget": 40.0,
                        "maxDehumidVent": 15.0,
                        "maxDehumidHeat": 10.0,
                        "co2Target": 800,
                        "spareTarget": 0
                    }
                }
            ]
        }
    ]
}
```
