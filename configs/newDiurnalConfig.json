{"hubId": "h1", "zoneId": "z1", "schedules": [{"id": "I1", "name": "Diurnal Setpoint", "type": "diurnal", "enabled": true, "periods": [{"id": "p1", "name": "Morning Period", "enabled": true, "startTimeType": "afterDawn", "startTimeValue": "00:30", "endTimeType": "afterDawn", "endTimeValue": "03:00", "activeDays": {"friday": true, "monday": true, "saturday": true, "sunday": true, "thursday": true, "tuesday": true, "wednesday": true}, "setpoints": [{"name": "co2Target", "value": 400, "capability": "co2", "unit": "ppm"}, {"name": "coolingTarget", "value": 22, "capability": "temperature", "unit": "C"}, {"name": "dehumidifyHeatTarget", "value": 70, "capability": "humidity", "unit": "%"}, {"name": "dehumidifyVentTarget", "value": 40, "capability": "humidity", "unit": "%"}, {"name": "heatingTarget", "value": 18, "capability": "temperature", "unit": "C"}, {"name": "maxDehumidHeat", "value": 5, "capability": "temperature", "unit": "%"}, {"name": "maxDehumidVent", "value": 10, "capability": "temperature", "unit": "%"}]}, {"id": "p2", "name": "Afternoon Period", "enabled": true, "startTimeType": "beforeD<PERSON>", "startTimeValue": "02:00", "endTimeType": "afterDusk", "endTimeValue": "00:30", "activeDays": {"friday": true, "monday": true, "saturday": true, "sunday": true, "thursday": true, "tuesday": true, "wednesday": true}, "setpoints": [{"name": "co2Target", "value": 1000, "capability": "co2", "unit": "ppm"}, {"name": "coolingTarget", "value": 26, "capability": "temperature", "unit": "C"}, {"name": "dehumidifyHeatTarget", "value": 50, "capability": "humidity", "unit": "%"}, {"name": "dehumidifyVentTarget", "value": 60, "capability": "humidity", "unit": "%"}, {"name": "heatingTarget", "value": 22, "capability": "temperature", "unit": "C"}, {"name": "maxDehumidHeat", "value": 15, "capability": "temperature", "unit": "%"}, {"name": "maxDehumidVent", "value": 30, "capability": "temperature", "unit": "%"}]}]}, {"id": "I2", "name": "Diurnal Setpoint", "type": "diurnal", "enabled": true, "periods": [{"id": "p1", "name": "Pre-Dawn Period", "enabled": true, "startTimeType": "afterDusk", "startTimeValue": "01:00", "endTimeType": "afterDawn", "endTimeValue": "00:00", "activeDays": {"friday": true, "monday": true, "saturday": true, "sunday": true, "thursday": true, "tuesday": true, "wednesday": true}, "setpoints": [{"name": "co2Target", "value": 500, "capability": "co2", "unit": "ppm"}, {"name": "coolingTarget", "value": 22, "capability": "temperature", "unit": "C"}, {"name": "dehumidifyHeatTarget", "value": 65, "capability": "humidity", "unit": "%"}, {"name": "dehumidifyVentTarget", "value": 50, "capability": "humidity", "unit": "%"}, {"name": "heatingTarget", "value": 19, "capability": "temperature", "unit": "C"}, {"name": "maxDehumidHeat", "value": 7, "capability": "temperature", "unit": "%"}, {"name": "maxDehumidVent", "value": 12, "capability": "temperature", "unit": "%"}]}, {"id": "p2", "name": "Midday Period", "enabled": true, "startTimeType": "afterDawn", "startTimeValue": "03:30", "endTimeType": "beforeD<PERSON>", "endTimeValue": "02:30", "activeDays": {"friday": true, "monday": true, "saturday": true, "sunday": true, "thursday": true, "tuesday": true, "wednesday": true}, "setpoints": [{"name": "co2Target", "value": 900, "capability": "co2", "unit": "ppm"}, {"name": "coolingTarget", "value": 25, "capability": "temperature", "unit": "C"}, {"name": "dehumidifyHeatTarget", "value": 55, "capability": "humidity", "unit": "%"}, {"name": "dehumidifyVentTarget", "value": 65, "capability": "humidity", "unit": "%"}, {"name": "heatingTarget", "value": 21, "capability": "temperature", "unit": "C"}, {"name": "maxDehumidHeat", "value": 12, "capability": "temperature", "unit": "%"}, {"name": "maxDehumidVent", "value": 25, "capability": "temperature", "unit": "%"}]}]}]}